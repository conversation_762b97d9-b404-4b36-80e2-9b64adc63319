import { defHttp } from '/@/utils/axios/index'
import { DataPermissionItem } from '/@/views/team/types'

enum Api {
  DataPermissionList = '/dataPermission/list',
  DataPermissionCreate = '/dataPermission/create',
  DataPermissionUpdate = '/dataPermission/update',
  DataPermissionDelete = '/dataPermission/delete',
  DataPermissionDetail = '/dataPermission/detail'
}

// 获取数据权限列表
export const getDataPermissionList = (params?: any) => {
  return defHttp.get({ url: Api.DataPermissionList, params })
}

// 创建数据权限
export const createDataPermission = (data: DataPermissionItem) => {
  return defHttp.post({ url: Api.DataPermissionCreate, data })
}

// 更新数据权限
export const updateDataPermission = (data: DataPermissionItem) => {
  return defHttp.post({ url: Api.DataPermissionUpdate, data })
}

// 删除数据权限
export const deleteDataPermission = (id: number) => {
  return defHttp.post({ url: Api.DataPermissionDelete, data: { id } })
}

// 获取数据权限详情
export const getDataPermissionDetail = (id: number) => {
  return defHttp.get({ url: Api.DataPermissionDetail, params: { id } })
}